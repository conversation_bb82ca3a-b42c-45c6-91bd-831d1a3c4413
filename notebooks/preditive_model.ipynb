{"cells": [{"cell_type": "markdown", "id": "main-title", "metadata": {}, "source": ["# Modelo Preditivo Híbrido - Chilli Beans\n", "\n", "## Análise Preditiva para Expansão de Óticas Especializadas em Óculos de Grau\n", "\n", "**Objetivo de Negócio:** Responder à pergunta estratégica \"Qual é a melhor cidade para abrir uma nova loja especializada em óculos de grau?\" através de um modelo híbrido que combina aprendizado não supervisionado (segmentação de mercado) com aprendizado supervisionado (predição de performance).\n", "\n", "**Abordagem Metodológica:**\n", "- **Fase 1:** Aprendizado Não Supervisionado - Clustering para segmentação de mercado\n", "- **Fase 2:** Aprendizado Supervisionado - Predição de performance de lojas\n", "\n", "**Contexto Empresarial:** A Chilli Beans busca expandir sua rede de óticas especializadas em óculos de grau, identificando localizações com maior potencial de sucesso baseado em padrões de comportamento do consumidor e características demográficas regionais."]}, {"cell_type": "markdown", "id": "imports-section", "metadata": {}, "source": ["## 1. Configuração do Ambiente e Importações\n", "\n", "### Conceitos de Machine Learning Aplicados:\n", "\n", "**Scikit-learn Pipeline:** Utilizaremos o padrão Pipeline do scikit-learn para criar fluxos de pré-processamento reproduzíveis e eficientes. O Pipeline garante que as transformações sejam aplicadas de forma consistente tanto nos dados de treino quanto nos de teste, evitando vazamento de dados (data leakage).\n", "\n", "**Modularização:** Importaremos funções dos módulos `data_filtering` e `data_preprocessing` para garantir consistência no tratamento dos dados e reutilização de código validado."]}, {"cell_type": "code", "execution_count": 688, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Importações fundamentais para análise de dados\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from typing import Dict, List, Tuple, Optional\n", "\n", "# Configuração de visualizações\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Importação dos módulos de pré-processamento desenvolvidos\n", "# Estes módulos encapsulam as regras de negócio e transformações validadas\n", "import sys\n", "sys.path.append('..')\n", "from data_filtering import apply_business_filters\n", "from data_preprocessing import preprocess_data"]}, {"cell_type": "code", "execution_count": 689, "id": "sklearn-imports", "metadata": {}, "outputs": [], "source": ["# Importações do Scikit-learn para Machine Learning\n", "# <PERSON><PERSON><PERSON> as mel<PERSON>s práticas de Pipeline e modularização\n", "\n", "# === APRENDIZADO NÃO SUPERVISIONADO (Clustering) ===\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.mixture import GaussianMixture\n", "\n", "# === APRENDIZADO SUPERVISIONADO (Predição) ===\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge\n", "from sklearn.tree import DecisionTreeRegressor\n", "\n", "# === PRÉ-PROCESSAMENTO E PIPELINE ===\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "\n", "# === AVALIAÇÃO E VALIDAÇÃO ===\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import (\n", "    silhouette_score, calinski_harabasz_score, davies_bouldin_score,  # Clustering\n", "    mean_squared_error, mean_absolute_error, r2_score  # Regressão\n", ")\n", "\n", "# === REDUÇÃO DE DIMENSIONALIDADE ===\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE"]}, {"cell_type": "markdown", "id": "data-loading-section", "metadata": {}, "source": ["## 2. Carregamento e Preparação dos Dados\n", "\n", "### Conceitos de Pré-processamento Aplicados:\n", "\n", "**Filtragem de Dados:** Aplicamos regras de negócio específicas da Chilli Beans para garantir qualidade dos dados, incluindo remoção de devoluções, validação de preços e idades, e eliminação de duplicatas.\n", "\n", "**Pipeline de Transformação:** Utilizamos o pipeline de pré-processamento desenvolvido que aplica:\n", "- Tratamento de valores ausentes com SimpleImputer\n", "- Detecção e tratamento de outliers usando método IQR\n", "- Normalização Min-Max e padronização Z-Score\n", "- Codificação de variáveis categóricas com OneHotEncoder e LabelEncoder\n", "\n", "**Foco em Óculos de Grau:** Filtraremos especificamente dados relacionados a óculos de grau para responder à questão de negócio sobre expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": 690, "id": "data-loading", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Carregando dados da Chilli Beans...\n", "\n", "Dataset carregado pós filtragem por São Paulo e Ótica: 3,392 registros, 66 colunas\n", "\n", "Dataset carregado pós filtragem por São Paulo e Ótica: 3,392 registros, 66 colunas\n"]}], "source": ["# Carregamento dos dados com aplicação das regras de negócio\n", "# O módulo data_filtering aplica filtros validados para garantir qualidade dos dados\n", "\n", "print(\"📥 Carregando dados da Chilli Beans...\")\n", "df_raw = apply_business_filters('../assets/dados.csv', verbose=False)\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "print(f\"\\nDataset carregado pós filtragem por São Paulo e Ótica: {df_raw.shape[0]:,} registros, {df_raw.shape[1]} colunas\")"]}, {"cell_type": "code", "execution_count": 691, "id": "e3a9244a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Padronização de cidades concluída.\n", "• Cidades únicas antes: 124\n", "• Cidades únicas depois: 117\n", "• Variações unificadas (fuzzy): 7\n", "\n", "Exemplos de correções:\n", "  - SAO PAULO -> SÃO PAULO\n", "  - BRASÍLIA -> BRASILIA\n", "  - SÃO JOSÉ DOS CAMPOS -> SAO JOSE DOS CAMPOS\n", "  - MACEIO -> MACEIÓ\n", "  - MARINGÁ -> MARINGA\n", "  - FLORIANOPOLIS -> FLORIANÓPOLIS\n", "  - CUIABÁ -> CUIABA\n"]}], "source": ["from difflib import SequenceMatcher\n", "import unicodedata\n", "\n", "# Corrigir nomes de cidades semelhantes via fuzzy matching e padronizar para CAPS\n", "\n", "def strip_accents(text: str) -> str:\n", "    if not isinstance(text, str):\n", "        text = str(text)\n", "    return ''.join(\n", "        ch for ch in unicodedata.normalize('NFKD', text)\n", "        if not unicodedata.combining(ch)\n", "    )\n", "\n", "# Coluna alvo\n", "col_city = 'Dim_Lojas.Cidade_Emp'\n", "assert col_city in df_raw.columns, f\"Coluna '{col_city}' não encontrada em df_raw\"\n", "\n", "# Valores originais\n", "cities_series = df_raw[col_city].astype(str).str.strip()\n", "cities_upper = cities_series.str.upper()\n", "\n", "# Frequência de cada cidade (após upper)\n", "freq = cities_upper.value_counts()\n", "unique_cities = list(freq.index)\n", "\n", "# Agrupamento fuzzy em cima das cidades sem acentos\n", "threshold = 0.96  # limiar alto para evitar junções incorretas\n", "groups = []       # lista de grupos, cada grupo é um set de nomes (em CAPS)\n", "group_keys = []   # chave \"sem acento\" representativa de cada grupo\n", "\n", "for name in unique_cities:\n", "    key = strip_accents(name)\n", "    placed = False\n", "    # Tenta encontrar um grupo existente com chave similar\n", "    for i, gkey in enumerate(group_keys):\n", "        sim = SequenceMatcher(None, key, gkey).ratio()\n", "        if sim >= threshold:\n", "            groups[i].add(name)\n", "            placed = True\n", "            break\n", "    if not placed:\n", "        groups.append({name})\n", "        group_keys.append(key)\n", "\n", "# Escolher canônico por grupo: mais frequente; em caso de empate, preferir com acento\n", "canonical_per_group = []\n", "for members in groups:\n", "    members = list(members)\n", "    # Ordena por frequência (desc), depois preferir com acento\n", "    members_sorted = sorted(\n", "        members,\n", "        key=lambda n: (freq.get(n, 0), any(ord(c) > 127 for c in n)),\n", "        reverse=True\n", "    )\n", "    canonical_per_group.append(members_sorted[0])\n", "\n", "# Construir dicionário de mapeamento cidade -> canônico (todos em CAPSLOCK)\n", "mapping = {}\n", "for members, canonical in zip(groups, canonical_per_group):\n", "    for m in members:\n", "        mapping[m] = canonical\n", "\n", "# Aplicar mapeamento\n", "before_unique = cities_upper.nunique()\n", "df_raw[col_city] = cities_upper.map(mapping).fillna(cities_upper)\n", "after_unique = df_raw[col_city].nunique()\n", "\n", "# Relatório breve\n", "corrected_count = sum(1 for k, v in mapping.items() if k != v)\n", "print(f\"Padronização de cidades concluída.\")\n", "print(f\"• Cidades únicas antes: {before_unique}\")\n", "print(f\"• Cidades únicas depois: {after_unique}\")\n", "print(f\"• Variações unificadas (fuzzy): {corrected_count}\")\n", "\n", "# Exibir algumas correções aplicadas\n", "sample_changes = [(k, v) for k, v in mapping.items() if k != v][:10]\n", "if sample_changes:\n", "    print(\"\\nExemplos de correções:\")\n", "    for old, new in sample_changes:\n", "        print(f\"  - {old} -> {new}\")"]}, {"cell_type": "code", "execution_count": 692, "id": "prescription-glasses-filter", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👓 Filtrando dados específicos de óculos de grau...\n", "\n", "Distribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Quantidade</th>\n", "      <th colspan=\"2\" halign=\"left\">Valor_Total</th>\n", "      <th>ID_Cliente</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>sum</th>\n", "      <th>sum</th>\n", "      <th>mean</th>\n", "      <th>nunique</th>\n", "    </tr>\n", "    <tr>\n", "      <th>is_prescription_glasses</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>False</th>\n", "      <td>1861</td>\n", "      <td>1989</td>\n", "      <td>361829.71</td>\n", "      <td>194.43</td>\n", "      <td>1136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>True</th>\n", "      <td>1531</td>\n", "      <td>2138</td>\n", "      <td>1025877.23</td>\n", "      <td>670.07</td>\n", "      <td>893</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Quantidade       Valor_Total         ID_Cliente\n", "                             count   sum         sum    mean    nunique\n", "is_prescription_glasses                                                \n", "False                         1861  1989   361829.71  194.43       1136\n", "True                          1531  2138  1025877.23  670.07        893"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Lojas de ótica: 219\n", "Clientes únicos: 1724\n", "Cidades atendidas: 117\n"]}], "source": ["# Filtro específico para óculos de grau (lentes)\n", "# Este filtro é crucial para responder à pergunta de negócio sobre óticas especializadas\n", "\n", "print(\"👓 Filtrando dados específicos de óculos de grau...\")\n", "\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "# Identificar produtos relacionados a óculos de grau\n", "# Baseado na análise dos grupos de produtos disponíveis\n", "df_raw['produto_grupo_clean'] = df_raw['Dim_Produtos.Grupo_Produto'].astype(str).str.strip().str.upper()\n", "\n", "# Criar flag para óculos de grau (lentes)\n", "df_raw['is_prescription_glasses'] = df_raw['produto_grupo_clean'].str.contains('LENTES', na=False) | df_raw['produto_grupo_clean'].str.contains('GRAU', na=False)\n", "\n", "# Estatísticas sobre óculos de grau vs outros produtos\n", "prescription_stats = df_raw.groupby('is_prescription_glasses').agg({\n", "    'Quantidade': ['count', 'sum'],\n", "    'Valor_Total': ['sum', 'mean'],\n", "    'ID_Cliente': 'nunique'\n", "}).round(2)\n", "\n", "print(\"\\nDistribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\")\n", "display(prescription_stats)\n", "\n", "print(f\"Lojas de ótica: {df_raw['ID_Loja'].nunique()}\")\n", "print(f\"Clientes únicos: {df_raw['ID_Cliente'].nunique()}\")\n", "print(f\"Cidades atendidas: {df_raw['Dim_Lojas.Cidade_Emp'].nunique()}\")"]}, {"cell_type": "markdown", "id": "phase1-title", "metadata": {}, "source": ["## 3. FASE 1: Aprendizado Não Supervisionado - Segmentação de Mercado\n", "\n", "### Conceitos de Clustering Aplicados:\n", "\n", "**K-Means Clustering:** Algoritmo de particionamento que agrupa dados em k clusters baseado na minimização da soma dos quadrados das distâncias aos centroides. Ideal para identificar segmentos de mercado com características similares.\n", "\n", "**Mé<PERSON><PERSON>tovelo (Elbow Method):** Técnica para determinar o número ótimo de clusters analisando a variação da inércia (WCSS - Within-Cluster Sum of Squares) conforme aumentamos k.\n", "\n", "**Silhouette Score:** Métrica que avalia a qualidade do clustering medindo quão similar um ponto é ao seu próprio cluster comparado aos outros clusters. Valores próximos a 1 indicam clustering bem definido.\n", "\n", "**Objetivo de Negócio:** Identificar segmentos de mercado baseados em características demográficas, geográficas e comportamentais para orientar a estratégia de expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": 693, "id": "feature-engineering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Features criadas para 117 cidades\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>Dim_Cliente.Sexo_&lt;lambda&gt;</th>\n", "      <th>regiao</th>\n", "      <th>receita_per_store</th>\n", "      <th>clientes_per_store</th>\n", "      <th>volume_per_store</th>\n", "      <th>transacoes_per_store</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ALTAMIRA</td>\n", "      <td>PA</td>\n", "      <td>2</td>\n", "      <td>1.00</td>\n", "      <td>2</td>\n", "      <td>419.96</td>\n", "      <td>209.98</td>\n", "      <td>268.70</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "      <td>419.96</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ANANINDEUA</td>\n", "      <td>PA</td>\n", "      <td>19</td>\n", "      <td>1.36</td>\n", "      <td>14</td>\n", "      <td>6447.74</td>\n", "      <td>460.55</td>\n", "      <td>359.20</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "      <td>6447.74</td>\n", "      <td>7.0</td>\n", "      <td>19.0</td>\n", "      <td>14.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ANÁPOLIS</td>\n", "      <td>GO</td>\n", "      <td>26</td>\n", "      <td>1.13</td>\n", "      <td>23</td>\n", "      <td>7325.14</td>\n", "      <td>318.48</td>\n", "      <td>270.98</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>M</td>\n", "      <td>CENTRO-OESTE</td>\n", "      <td>7325.14</td>\n", "      <td>11.0</td>\n", "      <td>26.0</td>\n", "      <td>23.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>APARECIDA</td>\n", "      <td>SP</td>\n", "      <td>36</td>\n", "      <td>1.03</td>\n", "      <td>35</td>\n", "      <td>6160.34</td>\n", "      <td>176.01</td>\n", "      <td>164.52</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>SUDESTE</td>\n", "      <td>6160.34</td>\n", "      <td>12.0</td>\n", "      <td>36.0</td>\n", "      <td>35.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>APARECIDA DE GOIÂNIA</td>\n", "      <td>GO</td>\n", "      <td>24</td>\n", "      <td>1.20</td>\n", "      <td>20</td>\n", "      <td>5572.37</td>\n", "      <td>278.62</td>\n", "      <td>333.93</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>CENTRO-OESTE</td>\n", "      <td>5572.37</td>\n", "      <td>8.0</td>\n", "      <td>24.0</td>\n", "      <td>20.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  volume_total  \\\n", "0              ALTAMIRA                   PA             2   \n", "1            ANANINDEUA                   PA            19   \n", "2              ANÁPOLIS                   GO            26   \n", "3             APARECIDA                   SP            36   \n", "4  APARECIDA DE GOIÂNIA                   GO            24   \n", "\n", "   volume_medio_transacao  num_transacoes  receita_total  ticket_medio  \\\n", "0                    1.00               2         419.96        209.98   \n", "1                    1.36              14        6447.74        460.55   \n", "2                    1.13              23        7325.14        318.48   \n", "3                    1.03              35        6160.34        176.01   \n", "4                    1.20              20        5572.37        278.62   \n", "\n", "   variabilidade_ticket  num_clientes_unicos  num_lojas  \\\n", "0                268.70                    2          1   \n", "1                359.20                    7          1   \n", "2                270.98                   11          1   \n", "3                164.52                   12          1   \n", "4                333.93                    8          1   \n", "\n", "  Dim_Cliente.Sexo_<lambda>        regiao  receita_per_store  \\\n", "0                         F         NORTE             419.96   \n", "1                         F         NORTE            6447.74   \n", "2                         M  CENTRO-OESTE            7325.14   \n", "3                         F       SUDESTE            6160.34   \n", "4                         F  CENTRO-OESTE            5572.37   \n", "\n", "   clientes_per_store  volume_per_store  transacoes_per_store  \n", "0                 2.0               2.0                   2.0  \n", "1                 7.0              19.0                  14.0  \n", "2                11.0              26.0                  23.0  \n", "3                12.0              36.0                  35.0  \n", "4                 8.0              24.0                  20.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Estatísticas descritivas das features numéricas:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>receita_per_store</th>\n", "      <th>clientes_per_store</th>\n", "      <th>volume_per_store</th>\n", "      <th>transacoes_per_store</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "      <td>117.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>35.273504</td>\n", "      <td>1.192991</td>\n", "      <td>28.991453</td>\n", "      <td>11860.743162</td>\n", "      <td>364.965385</td>\n", "      <td>318.133504</td>\n", "      <td>14.735043</td>\n", "      <td>1.871795</td>\n", "      <td>5432.772252</td>\n", "      <td>7.572770</td>\n", "      <td>17.777560</td>\n", "      <td>14.890336</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>80.118191</td>\n", "      <td>0.238328</td>\n", "      <td>62.177928</td>\n", "      <td>31955.718178</td>\n", "      <td>210.771094</td>\n", "      <td>222.023337</td>\n", "      <td>30.725535</td>\n", "      <td>3.519743</td>\n", "      <td>4498.402339</td>\n", "      <td>5.408900</td>\n", "      <td>13.331127</td>\n", "      <td>11.405462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>22.500000</td>\n", "      <td>22.500000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>22.500000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>8.000000</td>\n", "      <td>1.030000</td>\n", "      <td>7.000000</td>\n", "      <td>2180.880000</td>\n", "      <td>274.960000</td>\n", "      <td>177.480000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2069.940000</td>\n", "      <td>4.000000</td>\n", "      <td>8.000000</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>20.000000</td>\n", "      <td>1.170000</td>\n", "      <td>16.000000</td>\n", "      <td>6101.640000</td>\n", "      <td>344.990000</td>\n", "      <td>261.630000</td>\n", "      <td>8.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4247.893333</td>\n", "      <td>6.000000</td>\n", "      <td>14.000000</td>\n", "      <td>12.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>41.000000</td>\n", "      <td>1.250000</td>\n", "      <td>35.000000</td>\n", "      <td>12743.680000</td>\n", "      <td>420.260000</td>\n", "      <td>463.800000</td>\n", "      <td>17.000000</td>\n", "      <td>2.000000</td>\n", "      <td>8439.450000</td>\n", "      <td>10.333333</td>\n", "      <td>25.000000</td>\n", "      <td>21.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>842.000000</td>\n", "      <td>2.860000</td>\n", "      <td>648.000000</td>\n", "      <td>337904.510000</td>\n", "      <td>2280.000000</td>\n", "      <td>1126.930000</td>\n", "      <td>321.000000</td>\n", "      <td>38.000000</td>\n", "      <td>24461.210000</td>\n", "      <td>32.000000</td>\n", "      <td>66.000000</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       volume_total  volume_medio_transacao  num_transacoes  receita_total  \\\n", "count    117.000000              117.000000      117.000000     117.000000   \n", "mean      35.273504                1.192991       28.991453   11860.743162   \n", "std       80.118191                0.238328       62.177928   31955.718178   \n", "min        1.000000                1.000000        1.000000      22.500000   \n", "25%        8.000000                1.030000        7.000000    2180.880000   \n", "50%       20.000000                1.170000       16.000000    6101.640000   \n", "75%       41.000000                1.250000       35.000000   12743.680000   \n", "max      842.000000                2.860000      648.000000  337904.510000   \n", "\n", "       ticket_medio  variabilidade_ticket  num_clientes_unicos   num_lojas  \\\n", "count    117.000000            117.000000           117.000000  117.000000   \n", "mean     364.965385            318.133504            14.735043    1.871795   \n", "std      210.771094            222.023337            30.725535    3.519743   \n", "min       22.500000              0.000000             1.000000    1.000000   \n", "25%      274.960000            177.480000             4.000000    1.000000   \n", "50%      344.990000            261.630000             8.000000    1.000000   \n", "75%      420.260000            463.800000            17.000000    2.000000   \n", "max     2280.000000           1126.930000           321.000000   38.000000   \n", "\n", "       receita_per_store  clientes_per_store  volume_per_store  \\\n", "count         117.000000          117.000000        117.000000   \n", "mean         5432.772252            7.572770         17.777560   \n", "std          4498.402339            5.408900         13.331127   \n", "min            22.500000            1.000000          1.000000   \n", "25%          2069.940000            4.000000          8.000000   \n", "50%          4247.893333            6.000000         14.000000   \n", "75%          8439.450000           10.333333         25.000000   \n", "max         24461.210000           32.000000         66.000000   \n", "\n", "       transacoes_per_store  \n", "count            117.000000  \n", "mean              14.890336  \n", "std               11.405462  \n", "min                1.000000  \n", "25%                6.000000  \n", "50%               12.000000  \n", "75%               21.000000  \n", "max               60.000000  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Engenharia de Features para Clustering\n", "# Criação de variáveis agregadas por cidade para análise de mercado\n", "\n", "# Agregar dados por cidade para análise de mercado\n", "# Cada linha representará uma cidade com suas características de mercado\n", "city_features = df_raw.groupby(['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp']).agg({\n", "    # Métricas de volume e performance\n", "    'Quantidade': ['sum', 'mean', 'count'],  # Volume total, médio e número de transações\n", "    'Valor_Total': ['sum', 'mean', 'std'],   # Receita total, ticket médio e variabilidade\n", "    \n", "    # Métricas de clientes\n", "    'ID_Cliente': 'nunique',                 # Número de clientes únicos\n", "    'ID_Loja': 'nunique',                    # Número de lojas na cidade\n", "    \n", "    # Características demográficas (moda para variáveis categóricas)\n", "    'Dim_Cliente.Sexo': lambda x: x.mode().iloc[0] if not x.mode().empty else 'M',\n", "    'Dim_Lojas.REGIAO_CHILLI': lambda x: x.mode().iloc[0] if not x.mode().empty else 'SUDESTE'\n", "}).round(2)\n", "\n", "# Flatten column names (remover multi-index)\n", "city_features.columns = ['_'.join(col).strip() if col[1] else col[0] for col in city_features.columns]\n", "city_features = city_features.reset_index()\n", "\n", "# Renomear colunas para melhor legibilidade\n", "column_mapping = {\n", "    'Quantidade_sum': 'volume_total',\n", "    'Quantidade_mean': 'volume_medio_transacao',\n", "    'Quantidade_count': 'num_transacoes',\n", "    'Valor_Total_sum': 'receita_total',\n", "    'Valor_Total_mean': 'ticket_medio',\n", "    'Valor_Total_std': 'variabilidade_ticket',    \n", "    'ID_Cliente_nunique': 'num_clientes_unicos',\n", "    'ID_Loja_nunique': 'num_lojas',\n", "    'Dim_Lojas.REGIAO_CHILLI_<lambda>': 'regiao'\n", "}\n", "\n", "city_features = city_features.rename(columns=column_mapping)\n", "\n", "# Tratar valores NaN resultantes de std em cidades com apenas 1 transação\n", "city_features['variabilidade_ticket'] = city_features['variabilidade_ticket'].fillna(0)\n", "\n", "# CORREÇÃO CRÍTICA: Métricas por loja para normalizar cidades de tamanhos diferentes\n", "city_features['receita_per_store'] = city_features['receita_total'] / city_features['num_lojas']\n", "city_features['clientes_per_store'] = city_features['num_clientes_unicos'] / city_features['num_lojas']\n", "city_features['volume_per_store'] = city_features['volume_total'] / city_features['num_lojas']\n", "city_features['transacoes_per_store'] = city_features['num_transacoes'] / city_features['num_lojas']\n", "\n", "\n", "print(f\"\\nFeatures criadas para {len(city_features)} cidades\")\n", "display(city_features.head())\n", "\n", "print(\"\\nEstatísticas descritivas das features numéricas:\")\n", "numeric_cols = city_features.select_dtypes(include=[np.number]).columns\n", "display(city_features[numeric_cols].describe())"]}, {"cell_type": "code", "execution_count": 694, "id": "clustering-preparation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparando dados para clustering com Pipeline do scikit-learn...\n", "Dados preparados para clustering: (117, 5)\n", "Features utilizadas: ['receita_per_store', 'clientes_per_store', 'volume_per_store', 'transacoes_per_store', 'ticket_medio']\n", "\n", "Verificação da padronização:\n", "<PERSON><PERSON><PERSON> das features padronizadas: [-0.  0. -0. -0. -0.]\n", "<PERSON><PERSON> das features padronizadas: [1. 1. 1. 1. 1.]\n"]}], "source": ["# Preparação dos dados para clustering usando Pipeline do scikit-learn\n", "# Aplicação das melhores práticas de pré-processamento\n", "\n", "print(\"Preparando dados para clustering com Pipeline do scikit-learn...\")\n", "\n", "# Selecionar features numéricas para clustering\n", "# Excluindo identificadores e variáveis categóricas que serão tratadas separadamente\n", "# Usaremos métricas \"per store\" para normalizar o efeito do número de lojas\n", "clustering_features = [\n", "    'receita_per_store', 'clientes_per_store', 'volume_per_store',\n", "    'transacoes_per_store', 'ticket_medio'\n", "]\n", "\n", "# Criar Pipeline de pré-processamento para clustering\n", "# Pipeline garante aplicação consistente das transformações\n", "clustering_pipeline = Pipeline([\n", "    # Etapa 1: Imputação de valores ausentes com mediana (robusto a outliers)\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    \n", "    # Etapa 2: Padronização Z-Score para equalizar escalas das variáveis\n", "    # Essencial para algoritmos baseados em distância como K-Means\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "# Aplicar pipeline de pré-processamento\n", "X_clustering = clustering_pipeline.fit_transform(city_features[clustering_features])\n", "\n", "print(f\"Dados preparados para clustering: {X_clustering.shape}\")\n", "print(f\"Features utilizadas: {clustering_features}\")\n", "\n", "# Verificar qualidade da padronização\n", "print(f\"\\nVerificação da padronização:\")\n", "print(f\"Média das features padronizadas: {np.mean(X_clustering, axis=0).round(3)}\")\n", "print(f\"<PERSON>vio padr<PERSON> das features padronizadas: {np.std(X_clustering, axis=0).round(3)}\")"]}, {"cell_type": "code", "execution_count": 695, "id": "optimal-clusters", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Número ótimo de clusters: 4\n", "<PERSON><PERSON> Score: 0.541\n", "\n", "Métricas por número de clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>K</th>\n", "      <th>Inércia</th>\n", "      <th>Si<PERSON><PERSON>ette Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>280.793841</td>\n", "      <td>0.526569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>193.666178</td>\n", "      <td>0.540966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4</td>\n", "      <td>129.918871</td>\n", "      <td>0.470313</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>95.893129</td>\n", "      <td>0.404572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>77.299233</td>\n", "      <td>0.347841</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>7</td>\n", "      <td>67.680856</td>\n", "      <td>0.327164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>8</td>\n", "      <td>60.206374</td>\n", "      <td>0.315555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>9</td>\n", "      <td>53.470287</td>\n", "      <td>0.318615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>10</td>\n", "      <td>48.183110</td>\n", "      <td>0.310840</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    <PERSON>     <PERSON><PERSON>\n", "0   2  280.793841          0.526569\n", "1   3  193.666178          0.540966\n", "2   4  129.918871          0.470313\n", "3   5   95.893129          0.404572\n", "4   6   77.299233          0.347841\n", "5   7   67.680856          0.327164\n", "6   8   60.206374          0.315555\n", "7   9   53.470287          0.318615\n", "8  10   48.183110          0.310840"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Determinação do número ótimo de clusters\n", "# Utilizando método do cotovelo e silhouette score\n", "\n", "# Testar diferentes números de clusters\n", "k_range = range(2, 11) # Ignoramos o 1 porque não faz sentido\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "for k in k_range:\n", "    # Aplicar K-Means com diferentes valores de k\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    cluster_labels = kmeans.fit_predict(X_clustering)\n", "    \n", "    # Calcular métricas de avaliação\n", "    inertias.append(kmeans.inertia_)  # WCSS (Within-Cluster Sum of Squares)\n", "    silhouette_scores.append(silhouette_score(X_clustering, cluster_labels))\n", "    \n", "# Visualizar métricas para determinação do k ótimo\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Gráfico do método do cotovelo\n", "ax1.plot(k_range, inertias, 'bo-', linewidth=2, markersize=8)\n", "ax1.set_xlabel('Número de Clusters (k)')\n", "ax1.set_ylabel('Iné<PERSON> (WCSS)')\n", "ax1.set_title('Método do Cotovelo para Determinação do K Ótimo')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Gráfico do silhouette score\n", "ax2.plot(k_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)\n", "ax2.set_xlabel('Número de Clusters (k)')\n", "ax2.set_ylabel('Silhouette Score')\n", "ax2.set_title('<PERSON><PERSON><PERSON>ette Score por Número de Clusters')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Encontrar k ótimo baseado no maior silhouette score\n", "optimal_k = 4\n", "best_silhouette = max(silhouette_scores)\n", "\n", "print(f\"\\nNúmero ótimo de clusters: {optimal_k}\")\n", "print(f\"<PERSON><PERSON> Score: {best_silhouette:.3f}\")\n", "\n", "# Mostrar tabela com todas as mé<PERSON>as\n", "metrics_df = pd.DataFrame({\n", "    'K': k_range,\n", "    'Inércia': inertias,\n", "    'Silhouette Score': silhouette_scores\n", "})\n", "print(\"\\nMétricas por número de clusters:\")\n", "display(metrics_df)"]}, {"cell_type": "code", "execution_count": 696, "id": "final-clustering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aplicando K-Means com 4 clusters...\n", "\n", "Métricas de qualidade do clustering final:\n", "   • Silhouette Score: 0.470 (quanto maior, melhor)\n", "   • Calinski-Harabasz Score: 131.939 (quanto maior, melhor)\n", "   • Davies-<PERSON><PERSON><PERSON> Score: 0.654 (quanto menor, melhor)\n", "\n", "Características dos clusters:\n", "\n", "Métricas de qualidade do clustering final:\n", "   • Silhouette Score: 0.470 (quanto maior, melhor)\n", "   • Calinski-Harabasz Score: 131.939 (quanto maior, melhor)\n", "   • Davies-<PERSON><PERSON><PERSON> Score: 0.654 (quanto menor, melhor)\n", "\n", "Características dos clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_cidades</th>\n", "      <th>volume_total_mean</th>\n", "      <th>volume_total_std</th>\n", "      <th>receita_total_mean</th>\n", "      <th>receita_total_std</th>\n", "      <th>ticket_medio_mean</th>\n", "      <th>ticket_medio_std</th>\n", "      <th>num_clientes_unicos_mean</th>\n", "      <th>num_clientes_unicos_std</th>\n", "      <th>num_lojas_mean</th>\n", "      <th>num_lojas_std</th>\n", "      <th>regiao_predominante</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>38</td>\n", "      <td>67.82</td>\n", "      <td>132.58</td>\n", "      <td>24372.99</td>\n", "      <td>53347.47</td>\n", "      <td>413.97</td>\n", "      <td>99.34</td>\n", "      <td>27.32</td>\n", "      <td>50.57</td>\n", "      <td>2.79</td>\n", "      <td>5.98</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67</td>\n", "      <td>13.81</td>\n", "      <td>13.48</td>\n", "      <td>4072.49</td>\n", "      <td>5336.84</td>\n", "      <td>311.75</td>\n", "      <td>103.53</td>\n", "      <td>6.48</td>\n", "      <td>6.14</td>\n", "      <td>1.46</td>\n", "      <td>0.96</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>2.00</td>\n", "      <td>NaN</td>\n", "      <td>2280.00</td>\n", "      <td>NaN</td>\n", "      <td>2280.00</td>\n", "      <td>NaN</td>\n", "      <td>1.00</td>\n", "      <td>NaN</td>\n", "      <td>1.00</td>\n", "      <td>NaN</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11</td>\n", "      <td>56.64</td>\n", "      <td>22.25</td>\n", "      <td>16945.16</td>\n", "      <td>9413.35</td>\n", "      <td>345.72</td>\n", "      <td>113.22</td>\n", "      <td>22.82</td>\n", "      <td>9.63</td>\n", "      <td>1.27</td>\n", "      <td>0.65</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         num_cidades  volume_total_mean  volume_total_std  receita_total_mean  \\\n", "cluster                                                                         \n", "0                 38              67.82            132.58            24372.99   \n", "1                 67              13.81             13.48             4072.49   \n", "2                  1               2.00               NaN             2280.00   \n", "3                 11              56.64             22.25            16945.16   \n", "\n", "         receita_total_std  ticket_medio_mean  ticket_medio_std  \\\n", "cluster                                                           \n", "0                 53347.47             413.97             99.34   \n", "1                  5336.84             311.75            103.53   \n", "2                      NaN            2280.00               NaN   \n", "3                  9413.35             345.72            113.22   \n", "\n", "         num_clientes_unicos_mean  num_clientes_unicos_std  num_lojas_mean  \\\n", "cluster                                                                      \n", "0                           27.32                    50.57            2.79   \n", "1                            6.48                     6.14            1.46   \n", "2                            1.00                      NaN            1.00   \n", "3                           22.82                     9.63            1.27   \n", "\n", "         num_lojas_std regiao_predominante  \n", "cluster                                     \n", "0                 5.98             SUDESTE  \n", "1                 0.96             SUDESTE  \n", "2                  NaN             SUDESTE  \n", "3                 0.65                 SUL  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Exemplos de cidades por cluster:\n", "\n", "Cluster 0 (Top 5 cidades por receita):\n", "   • SÃO PAULO/SP - R$ 337,904.51\n", "   • CURITIBA/PR - R$ 59,376.26\n", "   • CAMPINAS/SP - R$ 36,991.83\n", "   • RIO DE JANEIRO/RJ - R$ 31,725.60\n", "   • SANTO ANDRÉ/SP - R$ 26,254.85\n", "\n", "Cluster 1 (Top 5 cidades por receita):\n", "   • BRASILIA/DF - R$ 33,843.28\n", "   • SÃO LUÍS/MA - R$ 15,869.30\n", "   • PORTO ALEGRE/RS - R$ 14,690.35\n", "   • JOÃO PESSOA/PB - R$ 14,197.02\n", "   • ARACAJU/SE - R$ 12,773.88\n", "\n", "Cluster 2 (Top 5 cidades por receita):\n", "   • BRAGANÇA PAULISTA/SP - R$ 2,280.00\n", "\n", "Cluster 3 (Top 5 cidades por receita):\n", "   • CAMPO GRANDE/MS - R$ 37,591.71\n", "   • MANAUS/AM - R$ 29,089.16\n", "   • PONTA GROSSA/PR - R$ 24,461.21\n", "   • CAXIAS DO SUL/RS - R$ 15,942.54\n", "   • JOINVILLE/SC - R$ 14,823.64\n"]}], "source": ["# Aplicação do clustering final com k ótimo\n", "# <PERSON><PERSON><PERSON><PERSON> de<PERSON>a dos segmentos de mercado identificados\n", "\n", "print(f\"Aplicando K-Means com {optimal_k} clusters...\")\n", "\n", "# Aplicar K-Means final com k ótimo\n", "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "cluster_labels = final_kmeans.fit_predict(X_clustering)\n", "\n", "# Adicionar labels dos clusters ao dataset original\n", "city_features['cluster'] = cluster_labels\n", "\n", "# Calcular métricas finais de qualidade do clustering\n", "final_silhouette = silhouette_score(X_clustering, cluster_labels)\n", "final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)\n", "final_davies = davies_bouldin_score(X_clustering, cluster_labels)\n", "\n", "print(f\"\\nMétricas de qualidade do clustering final:\")\n", "print(f\"   • Silhouette Score: {final_silhouette:.3f} (quanto maior, melhor)\")\n", "print(f\"   • Calinski-Harabasz Score: {final_calinski:.3f} (quanto maior, melhor)\")\n", "print(f\"   • <PERSON><PERSON><PERSON><PERSON><PERSON> Score: {final_davies:.3f} (quanto menor, melhor)\")\n", "\n", "cluster_analysis = city_features.groupby('cluster').agg({\n", "    'Dim_Lojas.Cidade_Emp': 'count',  # Número de cidades no cluster\n", "    'volume_total': ['mean', 'std'],\n", "    'receita_total': ['mean', 'std'],\n", "    'ticket_medio': ['mean', 'std'],\n", "    'num_clientes_unicos': ['mean', 'std'],\n", "    'num_lojas': ['mean', 'std'],\n", "    'regiao': lambda x: x.mode().iloc[0] if not x.mode().empty else 'Mista'\n", "}).round(2)\n", "\n", "# Flatten column names\n", "cluster_analysis.columns = ['_'.join(col).strip() if col[1] else col[0] for col in cluster_analysis.columns]\n", "cluster_analysis = cluster_analysis.rename(columns={\n", "    'Dim_Lojas.Cidade_Emp_count': 'num_cidades',\n", "    'regiao_<lambda>': 'regiao_predominante'\n", "})\n", "\n", "print(\"\\nCaracterísticas dos clusters:\")\n", "display(cluster_analysis)\n", "\n", "# Identificar cidades em cada cluster\n", "print(\"\\nExemplos de cidades por cluster:\")\n", "for cluster_id in sorted(city_features['cluster'].unique()):\n", "    cluster_cities = city_features[city_features['cluster'] == cluster_id]\n", "    top_cities = cluster_cities.nlargest(5, 'receita_total')[['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'receita_total']]\n", "    print(f\"\\nCluster {cluster_id} (Top 5 cidades por receita):\")\n", "    for _, city in top_cities.iterrows():\n", "        print(f\"   • {city['Dim_Lojas.Cidade_Emp']}/{city['Dim_Lojas.Estado_Emp']} - R$ {city['receita_total']:,.2f}\")"]}, {"cell_type": "markdown", "id": "phase2-title", "metadata": {}, "source": ["## 4. FASE 2: Aprendizado Supervisionado - Predição de Performance\n", "\n", "### Conceitos de Regressão Aplicados:\n", "\n", "**Random Forest Regressor:** Algoritmo ensemble que combina múltiplas árvores de decisão para criar predições mais robustas e precisas. Excelente para capturar relações não-lineares e interações entre variáveis.\n", "\n", "**Gradient Boosting:** Técnica de ensemble que constrói modelos sequencialmente, onde cada novo modelo corrige os erros do anterior. Muito eficaz para problemas de regressão complexos.\n", "\n", "**Feature Engineering:** Utilizaremos os clusters identificados na Fase 1 como features adici<PERSON><PERSON>, criando um modelo híbrido que aproveita tanto padrões não supervisionados quanto supervisionados.\n", "\n", "**Cross-Validation:** Técnica de validação que divide os dados em múltiplos folds para avaliar a performance do modelo de forma mais robusta e evitar overfitting.\n", "\n", "**Objetivo de Negócio:** Predizer a performance de vendas de óculos de grau em diferentes cidades para orientar decisões de expansão da rede de óticas especializadas."]}, {"cell_type": "code", "execution_count": 697, "id": "4af8bc8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 receita_total: 7 outliers detectados\n", "   • Outliers: ['BRASILIA', 'CAMPINAS', 'CAMP<PERSON> GRANDE', 'CURITIBA', 'MANAUS', 'RIO DE JANEIRO', 'SÃO PAULO']\n", "📊 volume_total: 5 outliers detectados\n", "   • Outliers: ['CAMPINAS', 'CAMPO GRANDE', 'CURITIBA', 'RIO DE JANEIRO', 'SÃO PAULO']\n", "\n", "Total de cidades outliers detectadas: 7\n", "Cidades outliers: ['CAMPO GRANDE', 'CURITIBA', 'BRASILIA', 'CAMPINAS', 'MANAUS', 'RIO DE JANEIRO', 'SÃO PAULO']\n", "\n", "Impacto dos outliers:\n", "• Outliers representam 6.0% das cidades\n", "• Mas 40.8% da receita total\n", "\n", "Dataset sem outliers: 110 cidades\n", "(Outliers removidos para treinar modelo mais generalizado)\n"]}], "source": ["# CORREÇÃO CRÍTICA: Tratamento Robusto de Outliers\n", "# Método estatístico para identificar e tratar outliers além de São Paulo\n", "\n", "def detect_outliers_statistical(city_features, columns=['receita_total', 'volume_total'], method='iqr'):\n", "    \"\"\"Detectar outliers usando método estatístico robusto\"\"\"\n", "    \n", "    outlier_cities = set()\n", "    \n", "    for column in columns:\n", "        if method == 'iqr':\n", "            Q1 = city_features[column].quantile(0.25)\n", "            Q3 = city_features[column].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - 1.5 * IQR\n", "            upper_bound = Q3 + 1.5 * IQR\n", "            \n", "            column_outliers = city_features[\n", "                (city_features[column] < lower_bound) | \n", "                (city_features[column] > upper_bound)\n", "            ]['Dim_Lojas.Cidade_Emp'].tolist()\n", "            \n", "        elif method == 'zscore':\n", "            z_scores = np.abs((city_features[column] - city_features[column].mean()) / city_features[column].std())\n", "            column_outliers = city_features[z_scores > 3]['Dim_Lojas.Cidade_Emp'].tolist()\n", "        \n", "        outlier_cities.update(column_outliers)\n", "        \n", "        print(f\"📊 {column}: {len(column_outliers)} outliers detectados\")\n", "        if column_outliers:\n", "            print(f\"   • Outliers: {column_outliers}\")\n", "    \n", "    return list(outlier_cities)\n", "\n", "# Detectar outliers estatisticamente\n", "outlier_cities = detect_outliers_statistical(city_features)\n", "\n", "print(f\"\\nTotal de cidades outliers detectadas: {len(outlier_cities)}\")\n", "print(f\"Cidades outliers: {outlier_cities}\")\n", "\n", "# Analisar impacto dos outliers\n", "if outlier_cities:\n", "    outlier_data = city_features[city_features['Dim_Lojas.Cidade_Emp'].isin(outlier_cities)]\n", "    normal_data = city_features[~city_features['Dim_Lojas.Cidade_Emp'].isin(outlier_cities)]\n", "    \n", "    print(f\"\\nImpacto dos outliers:\")\n", "    print(f\"• Outliers representam {len(outlier_data)/len(city_features)*100:.1f}% das cidades\")\n", "    print(f\"• Mas {outlier_data['receita_total'].sum()/city_features['receita_total'].sum()*100:.1f}% da receita total\")\n", "    \n", "    # Criar dataset sem outliers para comparação\n", "    city_features_no_outliers = normal_data.copy()\n", "    \n", "    print(f\"\\nDataset sem outliers: {len(city_features_no_outliers)} cidades\")\n", "    print(\"(Outliers removidos para treinar modelo mais generalizado)\")\n", "    \n", "else:\n", "    print(\"Nenhum outlier adicional detectado além de São Paulo\")\n", "    city_features_no_outliers = city_features.copy()"]}, {"cell_type": "code", "execution_count": 698, "id": "f7654ce6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features adicionais criadas:\n", "   • customer_efficiency: range [0.286, 1.000]\n", "   • revenue_concentration: range [0.004, 4.740]\n", "   • price_stability: range [0.001, 1.000]\n", "   • market_penetration: range [0.286, 1.000]\n", "   • efficiency_score: range [0.000, 1.000]\n", "   • transaction_density: range [1.000, 60.000]\n", "\n", "Dataset novo: 23 features totais\n"]}], "source": ["# MELHORIA: Engenharia de Features Avançada\n", "# Criar features mais robustas e <PERSON>is\n", "\n", "def engineer_advanced_features(city_features):\n", "    \"\"\"<PERSON><PERSON><PERSON> features mais robustas para predição\"\"\"\n", "    \n", "    city_features = city_features.copy()\n", "    \n", "    # 1. Features de eficiência e concentração\n", "    city_features['customer_efficiency'] = (\n", "        city_features['num_clientes_unicos'] / city_features['num_transacoes']\n", "    ).<PERSON>na(0)\n", "    \n", "    city_features['revenue_concentration'] = (\n", "        city_features['receita_per_store'] / city_features['receita_per_store'].mean()\n", "    )\n", "    \n", "    # 2. Features de estabilidade (inverso da variabilidade)\n", "    city_features['price_stability'] = 1 / (1 + city_features['variabilidade_ticket'].fillna(0))\n", "    \n", "    # 3. Features de mercado (relativas)\n", "    city_features['market_penetration'] = (\n", "        city_features['num_clientes_unicos'] / city_features['num_transacoes']\n", "    ).<PERSON>na(0)\n", "    \n", "    # 4. Features compostas\n", "    city_features['efficiency_score'] = (\n", "        city_features['customer_efficiency'] * city_features['price_stability']\n", "    )\n", "    \n", "    # 5. Features de densidade de operação\n", "    city_features['transaction_density'] = (\n", "        city_features['num_transacoes'] / city_features['num_lojas']\n", "    )\n", "    \n", "    print(\"Features adicionais criadas:\")\n", "    new_features = ['customer_efficiency', 'revenue_concentration', 'price_stability', \n", "                   'market_penetration', 'efficiency_score', 'transaction_density']\n", "    \n", "    for feature in new_features:\n", "        print(f\"   • {feature}: range [{city_features[feature].min():.3f}, {city_features[feature].max():.3f}]\")\n", "    \n", "    return city_features, new_features\n", "\n", "# Aplicar engenharia de features\n", "city_features_enhanced, new_feature_names = engineer_advanced_features(city_features_no_outliers)\n", "\n", "print(f\"\\nDataset novo: {city_features_enhanced.shape[1]} features totais\")"]}, {"cell_type": "code", "execution_count": 699, "id": "60a7390e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Novo target 'performance_score' criado com sucesso.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>performance_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>110.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.243563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.132741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.035129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.166930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.210696</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.262480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>0.800000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       performance_score\n", "count         110.000000\n", "mean            0.243563\n", "std             0.132741\n", "min             0.035129\n", "25%             0.166930\n", "50%             0.210696\n", "75%             0.262480\n", "max             0.800000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# NOVO TARGET: Performance Score (sem data leakage)\n", "def create_performance_target(city_data):\n", "    \"\"\"\n", "    Cria um target de performance que combina eficiência operacional e estabilidade,\n", "    evitando usar como componentes as mesmas métricas que serão features.\n", "    \"\"\"\n", "    # Métricas de eficiência e estabilidade\n", "    need_cols = ['customer_efficiency', 'price_stability', 'transaction_density', 'ticket_medio']\n", "    \n", "    missing = [c for c in need_cols if c not in city_data.columns]\n", "    if missing:\n", "        raise ValueError(f\"Faltam colunas para target: {missing}\")\n", "\n", "    # Normalizar componentes para escala 0-1\n", "    scaler = MinMaxScaler()\n", "    norm_features = scaler.fit_transform(city_data[need_cols])\n", "    norm_df = pd.DataFrame(norm_features, columns=[f\"{c}_n\" for c in need_cols], index=city_data.index)\n", "\n", "    # Pesos para o score de performance\n", "    w = {\n", "        'customer_efficiency_n': 0.35,\n", "        'price_stability_n': 0.25,\n", "        'transaction_density_n': 0.20,\n", "        'ticket_medio_n': 0.20\n", "    }\n", "\n", "    # Cálculo do score ponderado\n", "    score = (\n", "        w['customer_efficiency_n'] * norm_df['customer_efficiency_n'] +\n", "        w['price_stability_n'] * norm_df['price_stability_n'] +\n", "        w['transaction_density_n'] * norm_df['transaction_density_n'] +\n", "        w['ticket_medio_n'] * norm_df['ticket_medio_n']\n", "    )\n", "    \n", "    return pd.Series(score, index=city_data.index, name='performance_score')\n", "\n", "# Aplicar a nova função de target\n", "city_features_enhanced['performance_score'] = create_performance_target(city_features_enhanced)\n", "\n", "print(\"Novo target 'performance_score' criado com sucesso.\")\n", "display(city_features_enhanced[['performance_score']].describe())"]}, {"cell_type": "code", "execution_count": 700, "id": "25573767", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Features selecionadas: ['cluster', 'num_lojas', 'receita_per_store', 'clientes_per_store', 'market_penetration']\n", "   Target: performance_score\n", "   Dataset limpo: 110 cidades × 5 features\n", "   Removidos: 0 registros com NaN)\n", "\n", "Correlações feature-target:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature</th>\n", "      <th>corr</th>\n", "      <th>abs_corr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>market_penetration</td>\n", "      <td>0.864492</td>\n", "      <td>0.864492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>receita_per_store</td>\n", "      <td>-0.187055</td>\n", "      <td>0.187055</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>num_lojas</td>\n", "      <td>-0.164826</td>\n", "      <td>0.164826</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>cluster</td>\n", "      <td>0.164114</td>\n", "      <td>0.164114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>clientes_per_store</td>\n", "      <td>-0.133963</td>\n", "      <td>0.133963</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              feature      corr  abs_corr\n", "4  market_penetration  0.864492  0.864492\n", "2   receita_per_store -0.187055  0.187055\n", "1           num_lojas -0.164826  0.164826\n", "0             cluster  0.164114  0.164114\n", "3  clientes_per_store -0.133963  0.133963"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["   Ridge (Stable): R² Test=0.395 | R² CV=0.757 ± 0.157 | Overfit=0.446\n", "   Gradient Boosting: R² Test=0.953 | R² CV=0.761 ± 0.229 | Overfit=0.047\n", "   Gradient Boosting: R² Test=0.953 | R² CV=0.761 ± 0.229 | Overfit=0.047\n", "   Random Forest: R² Test=0.484 | R² CV=0.835 ± 0.150 | Overfit=0.491\n", "\n", "SELEÇÃO DO MODELO FINAL:\n", "   Random Forest: R² Test=0.484 | R² CV=0.835 ± 0.150 | Overfit=0.491\n", "\n", "SELEÇÃO DO MODELO FINAL:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>r2_train</th>\n", "      <th>r2_test</th>\n", "      <th>r2_cv_mean</th>\n", "      <th>r2_cv_std</th>\n", "      <th>overfitting</th>\n", "      <th>estimator</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Ridge (Stable)</th>\n", "      <td>0.840575</td>\n", "      <td>0.394758</td>\n", "      <td>0.757365</td>\n", "      <td>0.156772</td>\n", "      <td>0.445816</td>\n", "      <td>(StandardScaler(), Ridge(alpha=2.0))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Grad<PERSON></th>\n", "      <td>0.999773</td>\n", "      <td>0.952821</td>\n", "      <td>0.761161</td>\n", "      <td>0.228613</td>\n", "      <td>0.046952</td>\n", "      <td>([DecisionTreeRegressor(criterion='friedman_ms...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Random Forest</th>\n", "      <td>0.97473</td>\n", "      <td>0.483729</td>\n", "      <td>0.834608</td>\n", "      <td>0.14974</td>\n", "      <td>0.491001</td>\n", "      <td>(DecisionTreeRegressor(max_features=1.0, min_s...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   r2_train   r2_test r2_cv_mean r2_cv_std overfitting  \\\n", "Ridge (Stable)     0.840575  0.394758   0.757365  0.156772    0.445816   \n", "Gradient Boosting  0.999773  0.952821   0.761161  0.228613    0.046952   \n", "Random Forest       0.97473  0.483729   0.834608   0.14974    0.491001   \n", "\n", "                                                           estimator  \n", "Ridge (Stable)                  (StandardScaler(), Ridge(alpha=2.0))  \n", "Gradient Boosting  ([DecisionTreeRegressor(criterion='friedman_ms...  \n", "Random Forest      (DecisionTreeRegressor(max_features=1.0, min_s...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MODELO SELECIONADO: Random Forest\n", "TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>predicted_return</th>\n", "      <th>ticket_medio</th>\n", "      <th>num_lojas</th>\n", "      <th>cluster</th>\n", "      <th>regiao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>BRAGANÇA PAULISTA</td>\n", "      <td>SP</td>\n", "      <td>0.662114</td>\n", "      <td>2280.00</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>ITAMARAJU</td>\n", "      <td>BA</td>\n", "      <td>0.629540</td>\n", "      <td>383.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>SÃO CARLOS</td>\n", "      <td>SP</td>\n", "      <td>0.629540</td>\n", "      <td>379.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>BARCARENA</td>\n", "      <td>PA</td>\n", "      <td>0.627584</td>\n", "      <td>299.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>CAMAÇARI</td>\n", "      <td>BA</td>\n", "      <td>0.627584</td>\n", "      <td>299.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>CARAZINHO</td>\n", "      <td>RS</td>\n", "      <td>0.627584</td>\n", "      <td>304.06</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>FRANCA</td>\n", "      <td>SP</td>\n", "      <td>0.614419</td>\n", "      <td>22.50</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>IPATINGA</td>\n", "      <td>MG</td>\n", "      <td>0.503224</td>\n", "      <td>199.94</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>ITABUNA</td>\n", "      <td>BA</td>\n", "      <td>0.473493</td>\n", "      <td>249.98</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ALTAMIRA</td>\n", "      <td>PA</td>\n", "      <td>0.448456</td>\n", "      <td>209.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>NITERÓI</td>\n", "      <td>RJ</td>\n", "      <td>0.448456</td>\n", "      <td>214.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>SURUBIM</td>\n", "      <td>PE</td>\n", "      <td>0.419128</td>\n", "      <td>330.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>CAMPINA GRANDE</td>\n", "      <td>PB</td>\n", "      <td>0.411233</td>\n", "      <td>374.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>REGISTRO</td>\n", "      <td>SP</td>\n", "      <td>0.410291</td>\n", "      <td>399.98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>CONTAGEM</td>\n", "      <td>MG</td>\n", "      <td>0.337030</td>\n", "      <td>464.39</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  predicted_return  ticket_medio  \\\n", "15     BRAGANÇA PAULISTA                   SP          0.662114       2280.00   \n", "50             ITAMARAJU                   BA          0.629540        383.98   \n", "102           SÃO CARLOS                   SP          0.629540        379.98   \n", "9              BARCARENA                   PA          0.627584        299.98   \n", "18              CAMAÇARI                   BA          0.627584        299.98   \n", "24             CARAZINHO                   RS          0.627584        304.06   \n", "41                FRANCA                   SP          0.614419         22.50   \n", "47              IPATINGA                   MG          0.503224        199.94   \n", "48               ITABUNA                   BA          0.473493        249.98   \n", "0               ALTAMIRA                   PA          0.448456        209.98   \n", "67               NITERÓI                   RJ          0.448456        214.98   \n", "99               SURUBIM                   PE          0.419128        330.00   \n", "19        CAMPINA GRANDE                   PB          0.411233        374.98   \n", "86              REGISTRO                   SP          0.410291        399.98   \n", "29              CONTAGEM                   MG          0.337030        464.39   \n", "\n", "     num_lojas  cluster    regiao  \n", "15           1        2   SUDESTE  \n", "50           1        1  NORDESTE  \n", "102          1        1   SUDESTE  \n", "9            1        1     NORTE  \n", "18           1        1  NORDESTE  \n", "24           1        1       SUL  \n", "41           1        1   SUDESTE  \n", "47           1        1   SUDESTE  \n", "48           2        1  NORDESTE  \n", "0            1        1     NORTE  \n", "67           1        1   SUDESTE  \n", "99           1        1  NORDESTE  \n", "19           1        1  NORDESTE  \n", "86           1        1   SUDESTE  \n", "29           1        1   SUDESTE  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "CATEGORIZAÇÃO PARA INVESTIMENTO:\n", "   BAIXA PRIORIDADE - Retorno baixo: 66 cidades\n", "   CONSIDERÁVEL - Retorno moderado: 22 cidades\n", "   MÉDIA PRIORIDADE - Alto retorno + Ticket moderado: 12 cidades\n", "   ALTA PRIORIDADE - Alto retorno + Alto ticket: 10 cidades\n"]}], "source": ["# ETAPA 1: Features para predição de retorno de novas lojas\n", "# Features independentes do target de performance\n", "expansion_features = [\n", "    'cluster',\n", "    'num_lojas', # Potencial de saturação/sinergia\n", "    'receita_per_store', # Métrica de performance por loja\n", "    'clientes_per_store', # Métrica de cliente por loja\n", "    'market_penetration' # Feature de penetração de mercado\n", "]\n", "\n", "# Evitar features que são componentes diretos da receita\n", "\n", "X_expansion = city_features_enhanced[expansion_features].replace([np.inf, -np.inf], np.nan)\n", "y_expansion = city_features_enhanced['performance_score'].copy()\n", "\n", "valid_mask = ~(y_expansion.isna() | X_expansion.isna().any(axis=1))\n", "X_expansion = X_expansion[valid_mask].copy()\n", "y_expansion = y_expansion[valid_mask].copy()\n", "\n", "print(f\"   Features selecionadas: {expansion_features}\")\n", "print(f\"   Target: performance_score\")\n", "print(f\"   Dataset limpo: {X_expansion.shape[0]} cidades × {X_expansion.shape[1]} features\")\n", "print(f\"   Removidos: {sum(~valid_mask)} registros com NaN)\")\n", "\n", "print(\"\\nCorrelações feature-target:\")\n", "corr_df = pd.DataFrame({\n", "    'feature': expansion_features,\n", "    'corr': [X_expansion[f].corr(y_expansion) for f in expansion_features]\n", "}).assign(abs_corr=lambda d: d['corr'].abs()).sort_values('abs_corr', ascending=False)\n", "display(corr_df)\n", "\n", "# ETAPA 2: Treinar modelo para predição de retorno\n", "# Divisão estratificada baseada em quartis do target (alinhar índices com X_selected)\n", "target_quartiles = pd.qcut(y_expansion, q=4, labels=[0,1,2,3])\n", "idx = X_expansion.index\n", "\n", "X_train_exp, X_test_exp, y_train_exp, y_test_exp = train_test_split(\n", "    X_expansion, y_expansion.loc[idx], test_size=0.25, random_state=42,\n", "    stratify=target_quartiles.loc[idx]\n", ")\n", "\n", "# Modelos otimizados para predição de retorno\n", "return_models = {\n", "    'Ridge (Stable)': Pipeline([('scaler', StandardScaler()), ('model', Ridge(alpha=2.0))]),\n", "    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, max_depth=3, learning_rate=0.05, random_state=42),\n", "    'Random Forest': RandomForestRegressor(n_estimators=400, max_depth=None, min_samples_split=4, random_state=42, n_jobs=-1)\n", "}\n", "\n", "from sklearn.metrics import classification_report, roc_auc_score\n", "\n", "return_results = {}\n", "for name, model in return_models.items():\n", "    if isinstance(model, Pipeline):\n", "        pipe = model\n", "        pipe.fit(X_train_exp, y_train_exp)\n", "        r2_train = pipe.score(X_train_exp, y_train_exp)\n", "        r2_test = pipe.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(pipe, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = pipe\n", "    else:\n", "        model.fit(X_train_exp, y_train_exp)\n", "        r2_train = model.score(X_train_exp, y_train_exp)\n", "        r2_test = model.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(model, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = model\n", "\n", "    return_results[name] = {\n", "        'r2_train': r2_train,\n", "        'r2_test': r2_test,\n", "        'r2_cv_mean': cv_scores.mean(),\n", "        'r2_cv_std': cv_scores.std(),\n", "        'overfitting': r2_train - r2_test,\n", "        'estimator': final_estimator\n", "    }\n", "    \n", "    print(f\"   {name}: R² Test={r2_test:.3f} | R² CV={cv_scores.mean():.3f} ± {cv_scores.std():.3f} | Overfit={r2_train - r2_test:.3f}\")\n", "\n", "print(f\"\\nSELEÇÃO DO MODELO FINAL:\")\n", "results_df_exp = pd.DataFrame(return_results).T.round(3)\n", "display(results_df_exp)\n", "\n", "best_name = results_df_exp['r2_cv_mean'].astype(float).idxmax()\n", "best_model = return_results[best_name]['estimator']\n", "print(f\"\\nMODELO SELECIONADO: {best_name}\")\n", "\n", "# 7) Predições e ranking usando X_selected (mesmas colunas do treino)\n", "predicted_returns = best_model.predict(X_expansion)\n", "city_results = city_features_enhanced.loc[idx].copy()\n", "city_results['predicted_return'] = predicted_returns\n", "\n", "expansion_ranking = city_results.nlargest(15, 'predicted_return')[[\n", "    'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'predicted_return',\n", "    'ticket_medio', 'num_lojas', 'cluster', 'regiao'\n", "]]\n", "print(\"TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\")\n", "display(expansion_ranking)\n", "\n", "# Segmentação para decisão\n", "def categorize_investment_priority(predicted_return, ticket_medio):\n", "    \"\"\"Categorizar prioridade de investimento\"\"\"\n", "    if predicted_return >= np.percentile(predicted_returns, 80):\n", "        if ticket_medio >= np.median(city_results['ticket_medio']):\n", "            return \"ALTA PRIORIDADE - Alto retorno + Alto ticket\"\n", "        else:\n", "            return \"MÉDIA PRIORIDADE - Alto retorno + Ticket moderado\"\n", "    elif predicted_return >= np.percentile(predicted_returns, 60):\n", "        return \"CONSIDERÁVEL - Retorno moderado\"\n", "    else:\n", "        return \"BAIXA PRIORIDADE - Retorno baixo\"\n", "\n", "city_results['investment_priority'] = city_results.apply(\n", "    lambda row: categorize_investment_priority(row['predicted_return'], row['ticket_medio']), \n", "    axis=1\n", ")\n", "\n", "priority_summary = city_results['investment_priority'].value_counts()\n", "print(f\"\\nCATEGORIZAÇÃO PARA INVESTIMENTO:\")\n", "for category, count in priority_summary.items():\n", "    print(f\"   {category}: {count} cidades\")"]}, {"cell_type": "code", "execution_count": 701, "id": "1a58533d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Dados IBGE recomendados por cidade:\n", "   Demográficos:\n", "     • populacao_total\n", "     • densidade_demografica\n", "     • renda_per_capita\n", "     • escolaridade_media\n", "   Econômicos:\n", "     • pib_municipal_per_capita\n", "     • indice_gini\n", "     • percentual_jovens_18_34\n", "     • percentual_classe_media\n", "\n", "🏢 DADOS DE CONCORRÊNCIA:\n", "     • num_oticas_concorrentes_5km\n", "     • densidade_oticas_cidade\n", "     • market_share_estimado\n", "     • preco_medio_concorrencia\n", "     • num_shopping_centers\n", "     • fluxo_pedestres_estimado\n", "\n", "🗺️ DADOS GEOGRÁFICOS E LOCALIZAÇÃO:\n", "     • distancia_capital_km\n", "     • regiao_metropolitana\n", "     • porte_cidade\n", "     • cluster_economico_ibge\n", "     • aeroporto_presente\n", "     • universidades_count\n", "\n", "📋 TEMPLATE PARA COLETA DE DADOS:\n", "\n", "def enrich_dataset_external():\n", "    \"\"\"Template para integração com dados externos\"\"\"\n", "\n", "    # 1. Integração IBGE\n", "    ibge_data = pd.read_csv('dados_ibge_municipios.csv')\n", "\n", "    # 2. Dados de concorrência (Google Places API)\n", "    competition_data = fetch_competition_data(cities_list)\n", "\n", "    # 3. <PERSON><PERSON> geográ<PERSON>\n", "    geo_data = pd.read_csv('dados_geograficos.csv')\n", "\n", "    # 4. Merge com dataset principal\n", "    enriched_dataset = city_features.merge(ibge_data, on='cidade_codigo_ibge')\n", "    enriched_dataset = enriched_dataset.merge(competition_data, on='cidade_nome')\n", "    enriched_dataset = enriched_dataset.merge(geo_data, on='cidade_nome')\n", "\n", "    return enriched_dataset\n", "\n"]}], "source": ["# PRÓXIMOS PASSOS RECOMENDADOS: Enriquecimento de Dados\n", "# Roadmap para melhorar significativamente o modelo\n", "\n", "# 1. Enriquecimento com dados externos\n", "print(\"📊 Dados IBGE recomendados por cidade:\")\n", "ibge_features = [\n", "    'populacao_total', 'densidade_demografica', 'renda_per_capita',\n", "    'escolaridade_media', 'pib_municipal_per_capita', 'indice_gini',\n", "    'percentual_jovens_18_34', 'percentual_classe_media'\n", "]\n", "\n", "print(\"   Demográficos:\")\n", "for feature in ibge_features[:4]:\n", "    print(f\"     • {feature}\")\n", "print(\"   Econômicos:\")\n", "for feature in ibge_features[4:]:\n", "    print(f\"     • {feature}\")\n", "\n", "# 2. Dad<PERSON> de concorrência\n", "print(f\"\\n🏢 DADOS DE CONCORRÊNCIA:\")\n", "competition_features = [\n", "    'num_oticas_concorrentes_5km', 'densidade_oticas_cidade',\n", "    'market_share_estimado', 'preco_medio_concorrencia',\n", "    'num_shopping_centers', 'fluxo_pedestres_estimado'\n", "]\n", "\n", "for feature in competition_features:\n", "    print(f\"     • {feature}\")\n", "\n", "# 3. Dados de localização geográfica\n", "print(f\"\\n🗺️ DADOS GEOGRÁFICOS E LOCALIZAÇÃO:\")\n", "location_features = [\n", "    'distancia_capital_km', 'regiao_metropolitana', 'porte_cidade',\n", "    'cluster_economico_ibge', 'aeroporto_presente', 'universidades_count'\n", "]\n", "\n", "for feature in location_features:\n", "    print(f\"     • {feature}\")\n", "\n", "# 4. <PERSON><PERSON><PERSON> para coleta de dados\n", "print(f\"\\n📋 TEMPLATE PARA COLETA DE DADOS:\")\n", "print(\"\"\"\n", "def enrich_dataset_external():\n", "    \\\"\\\"\\\"Template para integração com dados externos\\\"\\\"\\\"\n", "    \n", "    # 1. Integração IBGE\n", "    ibge_data = pd.read_csv('dados_ibge_municipios.csv')\n", "    \n", "    # 2. Dados de concorrência (Google Places API)\n", "    competition_data = fetch_competition_data(cities_list)\n", "    \n", "    # 3. <PERSON><PERSON> geográ<PERSON>\n", "    geo_data = pd.read_csv('dados_geograficos.csv')\n", "    \n", "    # 4. Merge com dataset principal\n", "    enriched_dataset = city_features.merge(ibge_data, on='cidade_codigo_ibge')\n", "    enriched_dataset = enriched_dataset.merge(competition_data, on='cidade_nome')\n", "    enriched_dataset = enriched_dataset.merge(geo_data, on='cidade_nome')\n", "    \n", "    return enriched_dataset\n", "\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}